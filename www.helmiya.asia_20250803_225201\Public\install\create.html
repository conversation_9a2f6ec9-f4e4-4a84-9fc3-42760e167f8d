<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>筑梦视频安装程序</title>
<script type="text/javascript" src="../statics/js/jquery-2.0.0.min.js"></script>
<link rel="stylesheet" type="text/css" href="../statics/bootstrap-3.3.5/css/bootstrap.min.css">
<link rel="stylesheet" type="text/css" href="../statics/bootstrap-3.3.5/css/bootstrap-theme.min.css">
<link rel="stylesheet" type="text/css" href="../statics/font-awesome-4.4.0/css/font-awesome.min.css">
<link rel="stylesheet" type="text/css" href="../statics/css/bjy.css">
<script type="text/javascript" src="../statics/bootstrap-3.3.5/js/bootstrap.min.js"></script>
<!--[if lt IE 9]>
<script type="text/javascript" src="../statics/js/html5shiv.min.js"></script>
<script type="text/javascript" src="../statics/js/respond.min.js"></script>
<![endif]-->
<link rel="stylesheet" href="./css/install.css">
</head>
<body>
<div id="nav">
    <div class="inside">
        <p class="name">筑梦视频<span>安装向导</span></p>
        <ul class="schedule">
            <li class="number">1</li>
            <li class="word">使用协议</li>
        </ul>
        <ul class="schedule">
            <li class="number">2</li>
            <li class="word">环境检测</li>
        </ul>
        <ul class="schedule active">
            <li class="number">3</li>
            <li class="word">创建数据</li>
        </ul>
        <ul class="schedule">
            <li class="number">4</li>
            <li class="word">安装完成</li>
        </ul>
    </div>
</div>
<div id="out">
    <div class="inside">
        <div class="box create">
            <form class="form-inline" action="./index.php?c=success" method="post" >
                <h2>数据库信息</h2>
                <div class="one">
                    <label class="control-label">数据库类型</label>
                    <input class="form-control" type="text" name="DB_TYPE" value="mysql" disabled="disabled">
                </div>
                <div class="one">
                    <label class="control-label"> 数据库服务器</label>
                    <input class="form-control" type="text" name="DB_HOST" value="127.0.0.1">
                </div>
                <div class="one">
                    <label class="control-label"> 数据库端口</label>
                    <input class="form-control" type="text" name="DB_PORT" value="3306">
                </div>
                <div class="one">
                    <label class="control-label">数据库名</label>
                    <input class="form-control" type="text" name="DB_NAME">
                </div>
                <div class="one">
                    <label class="control-label">数据库用户名</label>
                    <input class="form-control" type="text" name="DB_USER" value="root">
                </div>
                <div class="one">
                    <label class="control-label"> 数据库密码</label>
                    <input class="form-control" type="text" name="DB_PWD">
                </div>
                <!-- <div class="one"> -->
                    <!-- <label class="control-label"> 数据表前缀</label> -->
                    <input class="form-control" type="hidden" name="DB_PREFIX" value="yn_">
                <!-- </div> -->
                <p class="agree">
                    <a class="btn btn-primary" href="./index.php?c=test">上一步</a>
                    <input class="btn btn-success" type="submit" value="确认">
                </p>
            </form>
        </div>
    </div>
</div>

</body>
</html>
