@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-add-tips-box.data-v-73408264 {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99999;
  opacity: 0.8;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
          justify-content: flex-end;
  -webkit-box-align: end;
  -webkit-align-items: flex-end;
          align-items: flex-end;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  width: 600rpx;
  -webkit-animation: opacityC-data-v-73408264 1s linear infinite;
          animation: opacityC-data-v-73408264 1s linear infinite;
}
.uni-add-tips-content.data-v-73408264::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  top: -38rpx;
  right: 105rpx;
  border-width: 20rpx;
  border-style: solid;
  display: block;
  border-color: transparent transparent #34b5e2 transparent;
}
.uni-add-tips-content.data-v-73408264 {
  border-width: 0rpx;
  margin-top: 20rpx;
  position: relative;
  background-color: #34b5e2;
  box-shadow: 0 10rpx 20rpx -10rpx #34b5e2;
  border-radius: 12rpx;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  padding: 18rpx 20rpx;
  margin-right: 40rpx;
}
.uni-add-tips-content > text.data-v-73408264 {
  color: #fff;
  font-size: 28rpx;
  font-weight: 400;
}
@-webkit-keyframes opacityC-data-v-73408264 {
0% {
    opacity: 0.8;
}
50% {
    opacity: 1;
}
}
@keyframes opacityC-data-v-73408264 {
0% {
    opacity: 0.8;
}
50% {
    opacity: 1;
}
}

