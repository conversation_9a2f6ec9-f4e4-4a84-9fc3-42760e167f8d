<?php
/**
 * 应用配置文件
 * 敏感信息已移至环境变量配置
 * 请复制 .env.example 为 .env 并填写正确的配置信息
 */

// 检查是否存在安全配置文件
$secureConfigFile = __DIR__ . '/config_secure.php';
if (file_exists($secureConfigFile)) {
    // 使用安全配置
    return include $secureConfigFile;
} else {
    // 兼容性配置（仅用于开发环境）
    return array(
        //*************************************数据库设置*************************************
        'DB_TYPE'               =>  'mysqli',
        'DB_HOST'               =>  'localhost',
        'DB_NAME'               =>  'your_database_name',  // 请在.env文件中配置
        'DB_USER'               =>  'your_database_user',  // 请在.env文件中配置
        'DB_PWD'                =>  'your_database_password',  // 请在.env文件中配置
        'DB_PORT'               =>  '3306',
        'DB_PREFIX'             =>  'zm_',

        // URL地址不区分大小写
        'URL_CASE_INSENSITIVE' => true,
        //REWRITE模式
        'URL_MODEL'=>'2',
        'MODULE_ALLOW_LIST'    =>    array('Zhis','App','Index'),
        'DEFAULT_MODULE'       =>    'Index',
        'DEFAULT_FILTER'        =>  'htmlspecialchars',

        // 安全提示
        'SECURITY_WARNING'      =>  'Please use config_secure.php and .env file for production!',
    );
}