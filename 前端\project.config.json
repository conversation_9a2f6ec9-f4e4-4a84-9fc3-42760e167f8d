{"description": "项目配置文件。", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": false, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true}, "compileType": "miniprogram", "libVersion": "2.15.0", "appid": "wxa4db4984611fa946", "projectname": "%E5%B0%8F%E4%BA%8C%E6%BA%90%E7%A0%81%E5%8D%9A%E5%AE%A2%E3%80%90vv.77zyw.top%E3%80%91", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}