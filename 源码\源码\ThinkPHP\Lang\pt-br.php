<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2014 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: <PERSON><PERSON> <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * ThinkPHP Portuguese language package
 */
return array(
    /* core language pachage */ 
    '_MODULE_NOT_EXIST_'     => "Módulo não pode ser carregado",
    '_CONTROLLER_NOT_EXIST_' => "Controller não pode ser carregado",
    '_ERROR_ACTION_'         => 'Ação ilegal',
    '_LANGUAGE_NOT_LOAD_'    => "Não é possível carregar pacote da linguagem",
    '_TEMPLATE_NOT_EXIST_'   => "Template não existe",
    '_MODULE_'               => 'Módulo',
    '_ACTION_'               => 'Ação',
    '_MODEL_NOT_EXIST_'      => "Modelo não pode ser carregado",
    '_VALID_ACCESS_'         => 'Sem acesso',
    '_XML_TAG_ERROR_'        => 'Erro de sintaxe - XML tag',
    '_DATA_TYPE_INVALID_'    => 'Tipos de dados ilegais!',
    '_OPERATION_WRONG_'      => 'Erro na operação',
    '_NOT_LOAD_DB_'          => 'Impossível carregar banco de dados',
    '_NO_DB_DRIVER_'         => 'Impossível carregar driver do bando de dados',
    '_NOT_SUPPORT_DB_'       => 'Temporariamente sem suporte ao banco',
    '_NO_DB_CONFIG_'         => 'Não define a configuração do banco',
    '_NOT_SUPPERT_'          => 'O sistema não suporta',
    '_CACHE_TYPE_INVALID_'   => 'Impossível carregar o tipo de cache',
    '_FILE_NOT_WRITEABLE_'   => 'Diretório (arquivo) não pode ser escrito',
    '_METHOD_NOT_EXIST_'     => 'O método solicitado não existe!',
    '_CLASS_NOT_EXIST_'      => 'Não existe instância da classe',
    '_CLASS_CONFLICT_'       => 'Conflitos com nome da classe',
    '_TEMPLATE_ERROR_'       => 'Erros na contrução do template',
    '_CACHE_WRITE_ERROR_'    => 'Escrita do arquivo de cache falhou!',
    '_TAGLIB_NOT_EXIST_'     => 'Biblioteca da tag não foi definida',
    '_OPERATION_FAIL_'       => 'Operação falhou!',
    '_OPERATION_SUCCESS_'    => 'Operação bem sucessida!',
    '_SELECT_NOT_EXIST_'     => 'Gravação não existe!',
    '_EXPRESS_ERROR_'        => 'Erros de expressão',
    '_TOKEN_ERROR_'          => 'Erro no token do formulário',
    '_RECORD_HAS_UPDATE_'    => 'Gravação não foi atualizada',
    '_NOT_ALLOW_PHP_'        => 'Código PHP não é permitido no template',
    '_PARAM_ERROR_'          => 'Parâmetro errado ou indefinido',
    '_ERROR_QUERY_EXPRESS_'  => 'Erros na expressão da query',       
);
