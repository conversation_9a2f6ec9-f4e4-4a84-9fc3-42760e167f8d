<!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title>{$config}总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="__PUBLIC__/admin/css/admin.min.css" media="all">
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">用户详情信息</div>
          <div class="layui-card-body" pad15>
            
            <div class="layui-form" wid100>

              <div class="layui-form-item">
                <label class="layui-form-label">用户名</label>
                <div class="layui-form-mid layui-word-aux">{$info.username}</div>
              </div>

           

              <div class="layui-form-item">
                <label class="layui-form-label">推荐码</label>
                <div class="layui-form-mid layui-word-aux">{$info.share}</div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">推荐人id</label>
                <div class="layui-form-mid layui-word-aux">{$info.pid}</div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">是否是代理</label>
                <div class="layui-form-mid layui-word-aux">
              
				<if condition="$vo.status == 1 ">
						 <span style="color: #009688;">否</span>
						<else/>
					    <span style="color: #FF5722;">是</span>
					   </if>
                </div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">联系方式</label>
                <div class="layui-form-mid layui-word-aux">{$info.phone}</div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-form-mid layui-word-aux">{$info.desc}</div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">余额</label>
                <div class="layui-form-mid layui-word-aux">{$info.money}</div>
              </div>
<div class="layui-form-item">
                <label class="layui-form-label">积分</label>
                <div class="layui-form-mid layui-word-aux">{$info.jifen}</div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">到期时间</label>
                <div class="layui-form-mid layui-word-aux">{$info.viptime|date="Y-m-d H:i:s", ###}</div>
              </div>

              <div class="layui-form-item">
                <label class="layui-form-label">注册时间</label>
                <div class="layui-form-mid layui-word-aux">{$info.addtime|date="Y-m-d H:i:s", ###}</div>
              </div>

               <div class="layui-form-item">
                <label class="layui-form-label">最近登录时间</label>
                <div class="layui-form-mid layui-word-aux">
                  {$vo.logintime|date="Y-m-d H:i:s",###}
                </div>
              </div>


              

              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn layui-btn-primary" onclick="javascript:history.back(-1);">返回</button>
                </div>
              </div>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>

<script src="__PUBLIC__/common/lib/layui/layui.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery-3.3.1.min.js"></script>
 <script type="text/javascript" src="__PUBLIC__/common/lib/jquery/jquery.cookie.js"></script>
  <script>
  layui.config({
    base: '__STATIC__/admin/js/' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'form'], function() {
    var $ = layui.$
        ,form = layui.form;
  });
  </script>
</body>
</html>