<!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title>{$config}总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="__PUBLIC__/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="__PUBLIC__/admin/css/admin.min.css" media="all">
		<script src="__PUBLIC__/admin/js/jquery-1.9.1.min.js"></script>        	
        <script src="__PUBLIC__/admin/assets/layer/layer.js" type="text/javascript" ></script>          
       
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space1">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
			
            <div class="layui-form" wid100>
              <div class="layui-form-item">
                <label class="layui-form-label">天数</label>
                <div class="layui-input-inline">
                  <input   value="1" autocomplete="off"type="number" name="fen" id="tian" class="layui-input">
                </div>
              </div>
			<div class="layui-form-item">
                <label class="layui-form-label">份数</label>
                <div class="layui-input-inline">
                  <input type="number" name="fen" id="fen"  value="1" autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  
                  <button class="layui-btn" onclick="tian1()">生成</button>
                  
                </div>
              </div>
            </div>
          
			<div class="layui-form" wid100>
              
			<div class="layui-form-item">
                <label class="layui-form-label">时间</label>
                <div class="layui-input-block">
                  <select name="position_id" name="type" style="display:block;width:140px;height:30px;" id="sss" >
                 
					<option value="1.5">月卡</option>
					<option value="4.5">季卡</option>
					<option value="9">半年</option>
					<option value="18">年卡</option>	
					<option value="150">永久</option>
					<option value="200">代理</option>
                  </select>
                </div>
              </div>
			  <div class="layui-form-item">
                <label class="layui-form-label">份数</label>
                <div class="layui-input-inline">
                  <input type="number" name="fen" id="fens"  value="1" autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block">
                  
                  <button class="layui-btn" onclick="tian()">生成</button>
                  
                </div>
              </div>
            </div>
		  
		  
		  
		  
          </div>
        </div>
      </div>
    </div>
  </div>

</body>

 <script type="text/javascript">
	function tian1() {

    var type   =   $('#tian').val();
    var fen     =   $('#fen').val();
    $.ajax({
        'type'  :   'post',
        'url'   :   '__URL__/kami1',
        'data'  :   {
            'type' :   type,
            'fen'   :   fen
        },
        'dataType'  :   'json',
        'success'   :   function (msg) {
	
            if(msg.code=='1')
            {
			
                layer.alert(msg.dian,{
                    btn:['txt导出','excel导出','取消'],
                    btn1: function(index, layero){
                        window.open('__URL__/xtxt.html?content='+msg.dian);
                    }
                    ,btn2: function(index, layero){
                        window.open('__URL__/xexcel.html?content='+msg.dian);
                        return false
                    }
                })
            }else{
                layer.alert(msg.dian);
            }
        },
        'error' :   function () {
            layer.alert('服务器错误');
        }
    })
}
	
	

function tian() {
//alert(1)
    var type   =   $('#sss').val();
    var fen     =   $('#fens').val();
    $.ajax({
        'type'  :   'post',
        'url'   :   '__URL__/kami',
        'data'  :   {
            'type' :   type,
            'fen'   :   fen
        },
        'dataType'  :   'json',
        'success'   :   function (msg) {

            if(msg.code=='1')
            {
			
			
                layer.alert(msg.dian,{
                    btn:['txt导出','excel导出','取消'],
                    btn1: function(index, layero){
                        window.open('__URL__/xtxt.html?content='+msg.dian);
                    }
                    ,btn2: function(index, layero){
                        window.open('__URL__/xexcel.html?content='+msg.dian);
                        return false
                    }
                })
            }else{
                layer.alert(msg.dian);
            }
        },
        'error' :   function () {
            layer.alert('服务器错误');
        }
    })
}
  

</script>
</html>