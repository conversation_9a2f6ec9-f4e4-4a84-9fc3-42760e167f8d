<?php
/**
 * 输入验证和过滤类
 * 用于统一处理用户输入的验证和过滤
 */
class InputValidator {
    
    /**
     * 验证和过滤用户ID
     * @param mixed $uid 用户ID
     * @return int 验证后的用户ID
     * @throws InvalidArgumentException
     */
    public static function validateUserId($uid) {
        $uid = intval($uid);
        if ($uid <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return $uid;
    }
    
    /**
     * 验证和过滤资源ID
     * @param mixed $tid 资源ID
     * @return int 验证后的资源ID
     * @throws InvalidArgumentException
     */
    public static function validateResourceId($tid) {
        $tid = intval($tid);
        if ($tid <= 0) {
            throw new InvalidArgumentException('Invalid resource ID');
        }
        return $tid;
    }
    
    /**
     * 验证和过滤搜索关键词
     * @param string $keyword 搜索关键词
     * @return string 验证后的关键词
     * @throws InvalidArgumentException
     */
    public static function validateKeyword($keyword) {
        $keyword = trim($keyword);
        if (empty($keyword)) {
            throw new InvalidArgumentException('Keyword cannot be empty');
        }
        if (strlen($keyword) > 100) {
            throw new InvalidArgumentException('Keyword too long');
        }
        // 移除危险字符
        $keyword = preg_replace('/[<>"\']/', '', $keyword);
        return htmlspecialchars($keyword, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 验证和过滤分页参数
     * @param mixed $page 分页参数
     * @return int 验证后的分页参数
     */
    public static function validatePageStart($page) {
        $page = intval($page);
        return $page < 0 ? 0 : $page;
    }
    
    /**
     * 验证和过滤价格
     * @param mixed $price 价格
     * @return int 验证后的价格
     * @throws InvalidArgumentException
     */
    public static function validatePrice($price) {
        $price = intval($price);
        if ($price < 0) {
            throw new InvalidArgumentException('Invalid price');
        }
        return $price;
    }
    
    /**
     * 验证和过滤用户名
     * @param string $username 用户名
     * @return string 验证后的用户名
     * @throws InvalidArgumentException
     */
    public static function validateUsername($username) {
        $username = trim($username);
        if (empty($username)) {
            throw new InvalidArgumentException('Username cannot be empty');
        }
        if (strlen($username) > 50) {
            throw new InvalidArgumentException('Username too long');
        }
        return htmlspecialchars($username, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 验证和过滤URL
     * @param string $url URL地址
     * @return string 验证后的URL
     * @throws InvalidArgumentException
     */
    public static function validateUrl($url) {
        $url = trim($url);
        if (!empty($url) && !filter_var($url, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException('Invalid URL format');
        }
        return $url;
    }
    
    /**
     * 验证和过滤标题
     * @param string $title 标题
     * @return string 验证后的标题
     * @throws InvalidArgumentException
     */
    public static function validateTitle($title) {
        $title = trim($title);
        if (empty($title)) {
            throw new InvalidArgumentException('Title cannot be empty');
        }
        if (strlen($title) > 200) {
            throw new InvalidArgumentException('Title too long');
        }
        return htmlspecialchars($title, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 验证和过滤微信授权码
     * @param string $code 授权码
     * @return string 验证后的授权码
     * @throws InvalidArgumentException
     */
    public static function validateWxCode($code) {
        $code = trim($code);
        if (empty($code)) {
            throw new InvalidArgumentException('WeChat code cannot be empty');
        }
        if (strlen($code) > 100) {
            throw new InvalidArgumentException('WeChat code too long');
        }
        // 只允许字母数字和部分特殊字符
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $code)) {
            throw new InvalidArgumentException('Invalid WeChat code format');
        }
        return $code;
    }
    
    /**
     * 安全过滤HTML内容
     * @param string $content HTML内容
     * @return string 过滤后的内容
     */
    public static function filterHtmlContent($content) {
        if (empty($content)) {
            return '';
        }
        
        // 允许的HTML标签
        $allowedTags = '<p><br><strong><em><ul><ol><li><h1><h2><h3><h4><h5><h6><blockquote><code><pre>';
        
        // 先解码，再过滤
        $content = htmlspecialchars_decode($content);
        $content = strip_tags($content, $allowedTags);
        
        // 移除危险属性
        $content = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);
        $content = preg_replace('/\s*javascript\s*:/i', '', $content);
        
        return $content;
    }
    
    /**
     * 通用输入过滤
     * @param mixed $input 输入值
     * @param string $type 数据类型
     * @return mixed 过滤后的值
     */
    public static function filter($input, $type = 'string') {
        switch ($type) {
            case 'int':
                return intval($input);
            case 'float':
                return floatval($input);
            case 'string':
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
            case 'email':
                return filter_var(trim($input), FILTER_VALIDATE_EMAIL);
            case 'url':
                return filter_var(trim($input), FILTER_VALIDATE_URL);
            default:
                return trim($input);
        }
    }
}
