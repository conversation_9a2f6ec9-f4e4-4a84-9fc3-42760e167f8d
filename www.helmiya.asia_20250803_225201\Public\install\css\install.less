@charset "utf-8";
/* CSS Document */
// 通用部分开始
.agree{
	width: 1000px;
	height: 60px;
	line-height: 60px;
	text-align: center;
}
.b-display-none{
	display: none;
}
// 通用部分结束

// 导航部分开始
#nav{
	width: 100%;
	height: 40px;
	background: #333333;
	color: #fff;
	.inside{
		margin: 0 auto;
		width: 1200px;
		height: 40px;
		.name{
			width: 200px;
			height: 40px;
			font-size: 30px;
			float: left;
			span{
				font-size: 16px;
			}

		}
		.schedule{
			margin-left: 10px;
			width: 200px;
			height: 40px;
			float: left;
			.number{
				width: 40px;
				height: 40px;
				line-height: 40px;
				text-align: center;
				font-size: 20px;
				border-radius: 50%;
				background: #80AAB7;
				float: left;
			}
			.word{
				width: 160px;
				height: 40px;
				float: left;
				line-height: 50px;
				text-indent: 10px;
				font-size: 16px;
			}
		}
		.active{
			.number{
				background: #008C<PERSON>;
			}
			.word{
				color: #008CBA;
			}
		}
	}

}
// 导航部分结束

#out{
	width: 100%;
	background: #F8F8F8;
	overflow: hidden;
	.inside{
		margin: 0 auto;
		padding: 20px;
		width: 1180px;
		background: #fff;
		box-shadow: 0px 0px 3px #ccc;
		overflow: hidden;
		.agreement{
			height: 600px;
			overflow: hidden;
			h2{
				height: 60px;
				line-height: 60px;
			}
			.content{
				font-size: 20px;
			}
			.admin_hint{
				color: red;
			}
		}
		.create{
			width: 1180px;
			.one{
				padding-left: 20px;
				width: 1180px;
				height: 40px;
				line-height: 40px;
				label{
					width: 100px;
				}
			}
		}
	}
}
