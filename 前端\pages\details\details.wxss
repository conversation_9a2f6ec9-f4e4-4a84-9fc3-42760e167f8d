page {
  background-color: #fff;
}
.scrool-page {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  width: 100%;
  height: 100%;
}
.list-cont {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
          flex: 1;
  position: relative;
}
.list-cont .scrool-more {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.footer {
  bottom: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  border-top: 1px solid #f6f6f6;
}
.gohome {
  -webkit-box-flex: 1;
  -webkit-flex: 1 25%;
          flex: 1 25%;
  background: #ffffff;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  height: 50px;
  padding-top: 8px;
  margin-top: 2px;
}
.liebiao {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  width: 100%;
  -webkit-flex-wrap: wrap;
          flex-wrap: wrap;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
          justify-content: flex-start;
  margin-top: 0.2rem;
  /* background-color: #212429; */
}
.liebiao-li {
  width: 20%;
  height: 5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  text-align: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  padding: 0.4rem 0px;
}
.liebiao-img {
  width: 25px;
  height: 25px;
  margin: 0.2rem auto;
  border-radius: 50%;
}
.liebiao-i {
  font-style: normal;
  color: rgba(41, 43, 51, 0.4);
  padding: 0.2rem 0px;
  font-size: 0.5rem;
}
.textnei {
  padding: 0px 15px 0px 15px;
  display: -webkit-box;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  background: #FAFAFA;
  opacity: 0.3;
}
.textnei1 {
  padding: 0px 15px 0px 15px;
}
.hidea {
  display: -webkit-box;
}
.showa {
  display: block;
  background: #FFFFFF;
  opacity: 10;
}
.lianv {
  width: 20%;
  height: 5rem;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  text-align: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  padding: 0.4rem 0px;
  background-color: #fff;
}
.lianv::after {
  border: none;
}
.district-fenrid {
  -webkit-box-flex: 1;
  -webkit-flex: 1 25%;
          flex: 1 25%;
  background: #ffffff;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
          flex-direction: column;
  height: 50px;
  padding-top: 8px;
  margin-top: 2px;
  background-color: #fff;
}
.district-fenrid::after {
  border: none;
}

