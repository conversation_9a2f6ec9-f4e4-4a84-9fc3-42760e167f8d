<?php
header("Content-Type: text/html;charset=utf-8");

// 安全检查：安装锁定
if(file_exists("./Public/install") && !file_exists("./Public/install/install.lock")){
 $url1 = $_SERVER['HTTP_HOST'].trim($_SERVER['SCRIPT_NAME'],'index.php').'Public/install/index.php';
   header("Location:http://$url1");
   die;
}

// PHP版本检查
if(version_compare(PHP_VERSION,'5.3.0','<'))  die('require PHP > 7.0.0 !');

// 加载环境变量
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $_ENV[trim($name)] = trim($value);
        }
    }
}

// 安全的调试模式配置
$appDebug = isset($_ENV['APP_DEBUG']) ? $_ENV['APP_DEBUG'] === 'true' : false;
$appEnv = isset($_ENV['APP_ENV']) ? $_ENV['APP_ENV'] : 'production';

// 生产环境强制关闭调试模式
if ($appEnv === 'production') {
    $appDebug = false;
}

define("APP_DEBUG", $appDebug);

// 如果是生产环境，隐藏错误信息
if (!$appDebug) {
    error_reporting(0);
    ini_set('display_errors', 0);
}

//定义当前项目应用目录
define("APP_PATH","./Application/");

//导入框架入口文件
require("./ThinkPHP/ThinkPHP.php");