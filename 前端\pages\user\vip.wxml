<view><view class="wrapper"><view class="my_top"><view class="my_top_c_b"><view class="user-tx"><image style="width:60px;height:60px;border-radius:50%;display:block;" src="{{userimg}}" mode></image><view style="width:15px;height:15px;position:absolute;bottom:0;right:6px;background:url(../../static/image/1.png) no-repeat;background-size:15px 15px;border-radius:50%;"></view></view><view class="m-user-title">{{usernc}}</view><block wx:if="{{tishi}}"><view class="user-yxw-vip-2">{{"VIP将于："+$root.f0+" 到期"}}</view></block><block wx:else><view class="user-yxw-vip-2">您目前还不是会员哦~</view></block></view></view><view class="m-dwz-viptc">一次开通，全站资源免费下</view><view class="post-form"><view class="recharge"><view data-event-opts="{{[['tap',[['change',[1,'$0'],['yiyue']]]]]}}" class="{{['pc-vip-list',(cityindex==1)?'active':'']}}" bindtap="__e"><view class="pc-vip-list2"><view class="pc-vip-name">一个月会员</view><view style="font-size:14px;color:#999;line-height:20px;">积分兑换</view></view><view class="money"><text style="font-size:12px;font-weight:700;color:#ff700a !important;">积分</text>{{yiyue+''}}</view></view><view data-event-opts="{{[['tap',[['change',[2,'$0'],['liuyue']]]]]}}" class="{{['pc-vip-list',(cityindex==2)?'active':'']}}" bindtap="__e"><view class="pc-vip-list2"><view class="pc-vip-name">六个月会员</view><view style="font-size:14px;color:#999;line-height:20px;">积分兑换</view></view><view class="money"><text style="font-size:12px;font-weight:700;color:#ff700a !important;">积分</text>{{liuyue+''}}</view></view><view data-event-opts="{{[['tap',[['change',[3,'$0'],['shier']]]]]}}" class="{{['pc-vip-list',(cityindex==3)?'active':'']}}" bindtap="__e"><view class="pc-vip-list2"><view class="pc-vip-name">十二个月会员</view><view style="font-size:14px;color:#999;line-height:20px;">积分兑换</view></view><view class="money"><text style="font-size:12px;font-weight:700;color:#ff700a !important;">积分</text>{{shier+''}}</view></view></view><button class="button2" type="submit" data-event-opts="{{[['tap',[['openSub']]]]}}" bindtap="__e">确认兑换</button><button class="button2" type="submit" data-event-opts="{{[['tap',[['openKami']]]]}}" bindtap="__e">激活卡密</button></view><view style="font-size:12px;margin-left:20px;margin-right:30px;color:#9CA0B8;"><view style="font-size:12px;">会员服务声明：</view><text>1.在会员有效期内，用户可以免费学习本站的所有课程。</text><view class="_br"></view><text>2.重复兑换会员服务，会员有效期将在原有基础上顺延。</text><view class="_br"></view><text>3.禁止用户使用任何方式，利用本站资源，为他人提供有偿服务，一经发现，本站有权查封该账号，并追究法律责任。</text></view></view></view>