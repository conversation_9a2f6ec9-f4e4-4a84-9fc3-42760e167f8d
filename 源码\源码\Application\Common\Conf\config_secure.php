<?php
/**
 * 安全配置文件
 * 使用环境变量管理敏感信息
 */

// 加载环境变量的简单实现
function loadEnv($file) {
    if (!file_exists($file)) {
        return;
    }
    
    $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_ENV)) {
            $_ENV[$name] = $value;
        }
    }
}

// 加载环境变量
loadEnv(__DIR__ . '/../../../.env');

// 获取环境变量的辅助函数
function env($key, $default = null) {
    return isset($_ENV[$key]) ? $_ENV[$key] : $default;
}

return array(
    //*************************************数据库设置*************************************
    'DB_TYPE'               =>  env('DB_TYPE', 'mysqli'),
    'DB_HOST'               =>  env('DB_HOST', 'localhost'),
    'DB_NAME'               =>  env('DB_NAME', ''),
    'DB_USER'               =>  env('DB_USER', ''),
    'DB_PWD'                =>  env('DB_PWD', ''),
    'DB_PORT'               =>  env('DB_PORT', '3306'),
    'DB_PREFIX'             =>  env('DB_PREFIX', 'zm_'),
    
    // URL地址不区分大小写
    'URL_CASE_INSENSITIVE' => true,
    
    //REWRITE模式
    'URL_MODEL'=>'2',
    'MODULE_ALLOW_LIST'    =>    array('Zhis','App','Index'),
    'DEFAULT_MODULE'       =>    'Index',
    'DEFAULT_FILTER'        =>  'htmlspecialchars',
    
    // 调试模式 - 生产环境应该设置为false
    'APP_DEBUG'             =>  env('APP_DEBUG', 'false') === 'true',
    
    // 安全配置
    'SECURITY_KEY'          =>  env('SECURITY_KEY', ''),
    'SESSION_PREFIX'        =>  env('SESSION_PREFIX', 'secure_'),
    
    // 微信配置
    'WX_APPID'              =>  env('WX_APPID', ''),
    'WX_APPSECRET'          =>  env('WX_APPSECRET', ''),
    
    // 缓存配置
    'DATA_CACHE_TYPE'       =>  env('CACHE_TYPE', 'file'),
    'DATA_CACHE_PREFIX'     =>  env('CACHE_PREFIX', 'app_cache_'),
    
    // 日志配置
    'LOG_LEVEL'             =>  env('LOG_LEVEL', 'error'),
    'LOG_PATH'              =>  env('LOG_PATH', './Runtime/Logs/'),
    
    // 输入过滤
    'DEFAULT_FILTER'        =>  'trim,htmlspecialchars',
    
    // 数据库字段类型检查
    'DB_FIELDTYPE_CHECK'    =>  true,
    
    // 开启字段缓存
    'DB_FIELDS_CACHE'       =>  true,
    
    // SQL调试
    'DB_DEBUG'              =>  env('APP_DEBUG', 'false') === 'true',
);
