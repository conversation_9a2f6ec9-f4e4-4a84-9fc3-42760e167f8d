
.tag-yuan {
	height: 10rpx;
	width: 30rpx;
	background-color: #000000;
	border-radius: 100rpx;
	position: absolute;
	top: 20rpx;
	left: 20rpx;
}

/* 标签样式 */
.tag-li {
	background-color: #FFFFFF;
	width: 30%;
	-webkit-flex-shrink: 0;
	        flex-shrink: 0;
	border-radius: 10rpx;
	height: 120rpx;
	margin: 8rpx;
	text-align: center;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-wrap: wrap;
	        flex-wrap: wrap;
	-webkit-box-align: center;
	-webkit-align-items: center;
	        align-items: center;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	        justify-content: center;
	font-size: 24rpx;
	position: relative;
}
.tag-view {
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: wrap;
	        flex-flow: wrap;
	width: 100%;
}

/* 列表没有数据 */
.no-list-data {
	text-align: center;
	margin: 30rpx 0px;
	color: #ADADAD;
	font-size: 20rpx;
}
.tag-w {
	height: 60rpx;
	line-height: 60rpx;
	border-radius: 10rpx;
	border: 1px #ececec solid;
	background-color: #ececec;
	padding: 0rpx 40rpx;
	font-size: 30rpx;
	color: #999;
	margin: 20rpx;
}
.tag-all {
	margin: 48rpx;
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-wrap: wrap;
	        flex-wrap: wrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	        justify-content: flex-start;
}
page {
	background-color: #F7F7F7;
	overflow-x: hidden;
}

/* 暗黑模式下应用的样式 */
@media (prefers-color-scheme: dark) {
page {
		background: #161616;
}
.tag-li {
		background: #202020
}
}

