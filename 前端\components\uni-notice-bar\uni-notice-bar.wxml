<block wx:if="{{show}}"><view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="uni-noticebar data-v-24a05de4" style="{{'background-color:'+(backgroundColor)+';'}}" bindtap="__e"><block wx:if="{{showClose===true||showClose==='true'}}"><uni-icons class="uni-noticebar-close data-v-24a05de4" vue-id="6f6bdfa2-1" type="closeempty" color="{{color}}" size="12" data-event-opts="{{[['^click',[['close']]]]}}" bind:click="__e" bind:__l="__l"></uni-icons></block><block wx:if="{{showIcon===true||showIcon==='true'}}"><uni-icons class="uni-noticebar-icon data-v-24a05de4" vue-id="6f6bdfa2-2" type="sound" color="{{color}}" size="14" bind:__l="__l"></uni-icons></block><view data-ref="textBox" class="{{['uni-noticebar__content-wrapper data-v-24a05de4 vue-ref',(scrollable)?'uni-noticebar__content-wrapper--scrollable':'',(!scrollable&&(single||moreText))?'uni-noticebar__content-wrapper--single':'']}}"><view class="{{['uni-noticebar__content data-v-24a05de4',(scrollable)?'uni-noticebar__content--scrollable':'',(!scrollable&&(single||moreText))?'uni-noticebar__content--single':'']}}" id="{{elIdBox}}"><text class="{{['uni-noticebar__content-text data-v-24a05de4 vue-ref',(scrollable)?'uni-noticebar__content-text--scrollable':'',(!scrollable&&(single||moreText))?'uni-noticebar__content-text--single':'']}}" style="{{'color:'+(color)+';'+('width:'+(wrapWidth+'px')+';')+('animation-duration:'+(animationDuration)+';')+('-webkit-animation-duration:'+(animationDuration)+';')+('animation-play-state:'+(webviewHide?'paused':animationPlayState)+';')+('-webkit-animation-play-state:'+(webviewHide?'paused':animationPlayState)+';')+('animation-delay:'+(animationDelay)+';')+('-webkit-animation-delay:'+(animationDelay)+';')}}" id="{{elId}}" data-ref="animationEle">{{text}}</text></view></view><block wx:if="{{showGetMore===true||showGetMore==='true'}}"><view data-event-opts="{{[['tap',[['clickMore',['$event']]]]]}}" class="uni-noticebar__more data-v-24a05de4" bindtap="__e"><block wx:if="{{moreText}}"><text class="uni-noticebar__more-text data-v-24a05de4" style="{{'color:'+(moreColor)+';'}}">{{moreText}}</text></block><uni-icons vue-id="6f6bdfa2-3" type="arrowright" color="{{moreColor}}" size="14" class="data-v-24a05de4" bind:__l="__l"></uni-icons></view></block></view></block>