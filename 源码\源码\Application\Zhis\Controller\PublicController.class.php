<?php
//学生信息控制类
namespace Zhis\Controller;
use Think\Controller;
class PublicController extends Controller {	
	public function login(){
		$config =M("config")->where("id=1")->find()['webname'];
		$this->assign("config",$config);
		$this->display("login");
	}
	//获取登录信息
	public function checkLogin(){

	// 输入验证和过滤
	$code = trim($_POST['vercode']);
	$username = trim($_POST['name']);
	$password = $_POST['pass'];

	// 基本验证
	if (empty($code) || empty($username) || empty($password)) {
		$this->assign("errorinfo","请填写完整的登录信息");
		$this->display("login");
		exit;
	}

	// 用户名长度限制
	if (strlen($username) > 50) {
		$this->assign("errorinfo","用户名过长");
		$this->display("login");
		exit;
	}

	// 密码长度限制
	if (strlen($password) > 100) {
		$this->assign("errorinfo","密码过长");
		$this->display("login");
		exit;
	}

	$flag=$this->check_verify($code);
	//验证码正确
	if($flag==0)
	{
		$this->assign("errorinfo","验证码错误，请重新填写");
        $this->display("login");
		exit;
	}

	// 防止SQL注入 - 使用参数化查询
	$username = htmlspecialchars($username, ENT_QUOTES, 'UTF-8');
	$admin = M("Admin")->where("name='%s'", $username)->find();
	if(empty($admin)){
		$this->assign("errorinfo","登录账号不存在，或已被禁用！");
		$this->display("login");
		exit();
		}

		// 使用更安全的密码验证
		$hashedPassword = md5(sha1($password));
		if($admin['pass'] === $hashedPassword){
            //此处表示登录成功
            // 清理敏感信息
            unset($admin['pass']);
            $_SESSION['adminuser']=$admin; //将登录成功的信息放入到session中

            // 记录登录日志
            $this->logLoginAttempt($username, true);

            $this->redirect("Index/index");
        }else{
            // 记录失败的登录尝试
            $this->logLoginAttempt($username, false);

            $this->assign("errorinfo","登录密码错误！");
            $this->display("login");
            exit();
        }
    }

    /**
     * 记录登录尝试
     */
    private function logLoginAttempt($username, $success) {
        $logData = array(
            'username' => $username,
            'success' => $success ? 1 : 0,
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'attempt_time' => time()
        );

        // 这里可以记录到日志文件或数据库
        error_log("Login attempt: " . json_encode($logData));
    }
	//验证码
	public function verifyImg(){
		  $config=array(
			'imageW'  => 150,
			'imageH'  => 40,
			'fontSize' => 20,
			'length'  => 4,
			'useNoise'    =>    false, // 关闭验证码杂点
			);
			$obj = new \Think\Verify($config);
			$obj->entry();
		}
	//验证
	function check_verify($code, $id = ''){     
       $verify = new \Think\Verify();     
       return $verify->check($code, $id); 
    }

	//执行退出
	public function loginOut(){
		unset($_SESSION['adminuser']);
		unset($_SESSION['auth_temp']);
		
		$this->redirect("Public/login");
	}

}