
page {
	background-color: #F7F7F7;
}
.postlist-bg {
		background-color: #FFFFFF;
		border-radius: 30rpx;
		overflow: hidden;
		margin: 20rpx;
} 
/* 内容数据 */
.menu-scroll-list-describe {
		color: #ADADAD;
		font-size: 20rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
}
.list-li-tag {
		color: #0BBDA6;
		font-size: 20rpx;
}
.menu-scroll-list-h {
		font-size: 28rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
}
.menu-scroll-list-right {
		margin-left: 32rpx;
		width: 420rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-pack: justify;
		-webkit-justify-content: space-between;
		        justify-content: space-between;
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-webkit-flex-flow: column;
		        flex-flow: column;
}
.menu-scroll-list-img {
		width: 360rpx;
		height: 160rpx;
		border-radius: 16rpx;
		overflow: hidden;
}
.menu-scroll-list {
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		margin: 20rpx 20rpx;
}
.fengrui-img {
	width: 100%;
	height: 100%;
}

